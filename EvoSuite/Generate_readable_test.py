import sys
import os
import json
import re
import shutil
import subprocess
from collections import defaultdict
from typing import List

# ———————————————————— ChatGPT 生成 ————————————————————
# 这个脚本批量为“指定方法列表”调用 EvoSuite，
# 并把生成的测试文件按 “<项目>_<方法>.java” 格式收集到 OUTPUT_DIR。
# 仅用于可读性评估，**不执行 JUnit**。
# ————————————————————————————————————————————————

sys.path.append("/Users/<USER>/Desktop/TestAgents")
from EnvironmentService import EnvironmentService
from Node import Method

# ◼︎ 环境准备
envServer = EnvironmentService()
PROJECT_SRC = "/Users/<USER>/Desktop/java-project/jfreechart-1.5.5"  # ← 修改到当前项目源码或 jar
PROJECT_NAME = "chart"                                           # ← chart / cli / csv …

evosuite_path = "/Users/<USER>/Desktop/TestAgents/evosuite-standalone-runtime-1.2.0.jar"
OUTPUT_DIR    = "/Users/<USER>/Desktop/TestAgents/EvoSuite/readable"

# EvoSuite 搜索配置（可自行调节）
EVOSUITE_ARGS = {
    "criterion": "LINE,BRANCH",
    "search_budget": "60",     # 每个方法搜索 60 秒
    "assertions": "true",      # 保留断言，提升可读性
    "no_runtime": "true"       # 生成后不再执行 JUnit 测试
}

# 用例筛选列表 —— 这里只演示 jFreeChart，其他项目自行切换
FILTER_METHODS: List[str] = [
    "javascriptEscape","windowActivated","getY","getLeft","getHeight","getType",
    "setPaint","isLeft","createWindPlot","trimWidth","getPaint","setPoint",
    "getRadius","transform","isTopOrBottom","compareTo","isVerticalCenter",
    "isMultiple","overlaps","isBaseline"
]

# focal_method 记录文件：由上游工具生成，包含 Method 元数据
FOCAL_JSONL = "/Users/<USER>/Desktop/TestAgents/TestAgent_wo_function_call/result/focal_method/chart_new.jsonl"

# ———————————————————— 辅助函数 ————————————————————

def read_jsonl(file_path: str) -> List[Method]:
    """反序列化 Method 对象列表。"""
    ret: List[Method] = []
    with open(file_path, "r", encoding="utf-8") as fp:
        for line in fp:
            data = json.loads(line)
            ret.append(Method(**data))  # Node.Method dataclass-like
    return ret


def build_target_method(m: Method) -> str:
    """拼接 EvoSuite 识别的 target_method 字符串。"""
    if not m.params:
        return m.name  # 无参方法
    # 参数类型列表 → foo(int,java.lang.String)
    param_types = ",".join([p.type for p in m.params])
    return f"{m.name}({param_types})"


def find_generated_test(tmp_dir: str, class_name: str) -> str:
    """在 tmp_dir/evosuite-tests/ 中定位 <ClassName>_ESTest.java。"""
    target_suffix = f"{class_name}_ESTest.java"
    for root, _, files in os.walk(os.path.join(tmp_dir, "evosuite-tests")):
        for f in files:
            if f == target_suffix:
                return os.path.join(root, f)
    raise FileNotFoundError(f"⚠️ 未发现 {target_suffix}")


def ensure_clean_workspace(tmp_dir: str):
    shutil.rmtree(os.path.join(tmp_dir, "evosuite-tests"), ignore_errors=True)


def run_evosuite(full_class: str, target_method: str, cp: str, work_dir: str):
    """调用 EvoSuite 生成指定方法的测试用例。"""
    ensure_clean_workspace(work_dir)
    cmd = [
        "java", "-jar", evosuite_path,
        "-class", full_class,
        "-projectCP", cp,
        f"-Dtarget_method={target_method}",
        *sum([[f"-D{k}", v] for k, v in EVOSUITE_ARGS.items()], [])
    ]
    print("  ↳", " ".join(cmd))
    subprocess.run(cmd, cwd=work_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)


def main():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    envServer.set_base_path(PROJECT_SRC)

    # 1. 解析 focal_method 列表
    focal_methods = read_jsonl(FOCAL_JSONL)

    # 2. 预估 classpath（源码 / jar + 依赖） —— 按需调整
    if PROJECT_SRC.endswith(".jar"):
        project_cp = PROJECT_SRC
    else:
        # 默认假设 Maven: target/classes + 依赖 jars
        project_cp = f"{PROJECT_SRC}/target/classes:{PROJECT_SRC}/target/dependency/*"

    generated = 0
    for m in focal_methods:
        if m.name not in FILTER_METHODS:
            continue
        full_class = f"{m.package_name}.{m.class_name}" if m.package_name else m.class_name
        target_method = build_target_method(m)
        try:
            run_evosuite(full_class, target_method, project_cp, PROJECT_SRC)
            src_test = find_generated_test(PROJECT_SRC, m.class_name)
            dst_test = os.path.join(OUTPUT_DIR, f"{PROJECT_NAME}_{m.name}.java")
            shutil.copy(src_test, dst_test)
            print(f"    ✓ 保存 {os.path.basename(dst_test)}")
            generated += 1
        except subprocess.CalledProcessError as ce:
            print(f"    ❌ EvoSuite 失败: {ce}")
        except Exception as err:
            print(f"    ⚠️  {err}")

    print("\n🎉 完成: 生成测试", generated, "个 →", OUTPUT_DIR)


if __name__ == "__main__":
    main()
