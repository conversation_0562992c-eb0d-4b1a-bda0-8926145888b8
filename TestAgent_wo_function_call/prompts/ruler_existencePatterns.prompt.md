You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior of the method, ensuring it returns a Patterns object with MatchType.EXISTS.', 'Input_Type': 'There are no input parameters for this method.', 'Output_Type': 'The method should return a Patterns object with its match type set to MatchType.EXISTS.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.MatchType;

public class PatternsTest {

    @Test
    public void testExistencePatterns() {
        // Act
        Patterns patterns = Patterns.existencePatterns();

        // Assert
        assertNotNull("Patterns object should not be null", patterns);
        assertEquals("The MatchType should be EXISTS", MatchType.EXISTS, patterns.type());
    }
}

Test Purpose: {'Test_Purpose': 'Verify behavior when testing boundary conditions on object creation.', 'Input_Type': 'Boundary testing is not applicable here due to absence of method parameters.', 'Output_Type': 'The method should consistently return a new Patterns object with match type MatchType.EXISTS.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;
import org.junit.Test;
import software.amazon.event.ruler.MatchType;
import software.amazon.event.ruler.Patterns;

public class PatternsTest {

    @Test
    public void testExistencePatterns() {
        // Act
        Patterns existencePattern = Patterns.existencePatterns();

        // Assert
        assertEquals("The match type should be EXISTS for existence patterns.",
                     MatchType.EXISTS,
                     existencePattern.type());
    }
}

Test Purpose: {'Test_Purpose': 'Verify handling of exceptional cases such as system constraints or object construction errors.', 'Input_Type': 'Exception testing is not directly applicable within this simple static method context.', 'Output_Type': 'The method should not throw any exceptions and should always return a valid Patterns object without failure.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertEquals;

import org.junit.Test;
import software.amazon.event.ruler.MatchType;
import software.amazon.event.ruler.Patterns;

public class PatternsTest {

    @Test
    public void testExistencePatterns() {
        // Act: Call the method under test
        Patterns result = Patterns.existencePatterns();

        // Assert: Verify that the result is not null
        assertNotNull("The method existencePatterns() should always return a valid Patterns object", result);

        // Assert: Verify that the product's type is MatchType.EXISTS
        assertEquals("The Patterns object should be initialized with MatchType.EXISTS", MatchType.EXISTS, result.type());
    }
}
