You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify that the method returns the nameState object when provided with a valid Patterns object', 'Input_Type': 'Valid Patterns object', 'Output_Type': 'The method should return the nameState object'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;

import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.NameState;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.SingleStateNameMatcher;

public class SingleStateNameMatcherTest {
    
    private SingleStateNameMatcher singleStateNameMatcher;
    private NameState mockNameState;

    @Before
    public void setUp() {
        // Assuming NameState is initialized properly inside SingleStateNameMatcher
        singleStateNameMatcher = new SingleStateNameMatcher();
    }

    @Test
    public void testFindPatternReturnsNameState() {
        Patterns patterns = mock(Patterns.class);

        // Act
        NameState result = singleStateNameMatcher.findPattern(patterns);

        // Assert that result is not null, since we can't verify equality without knowing internal init logic
        assertEquals(singleStateNameMatcher.getNameState(), result);
    }
}

Test Purpose: {'Test_Purpose': "Check the method's behavior with a boundary condition where a Patterns object is minimally populated or empty", 'Input_Type': 'Patterns object with minimal or no attributes set', 'Output_Type': 'The method should still return the nameState object, demonstrating it does not depend on pattern attributes'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.NameState;
import software.amazon.event.ruler.SingleStateNameMatcher;

public class SingleStateNameMatcherTest {

    private SingleStateNameMatcher singleStateNameMatcher;

    @Before
    public void setUp() {
        // Initialize SingleStateNameMatcher without parameter
        singleStateNameMatcher = new SingleStateNameMatcher();
    }

    @Test
    public void testFindPatternWithMinimalPatterns() {
        // Create a mock Patterns object
        Patterns minimalPatterns = mock(Patterns.class);

        // Call findPattern with the mock Patterns object
        NameState result = singleStateNameMatcher.findPattern(minimalPatterns);

        // Assert that the result is not null
        assertNotNull("The result should not be null.", result);
    }
}

Test Purpose: {'Test_Purpose': "Validate the method's behavior when a null Patterns object input is attempted", 'Input_Type': 'Null to examine exception handling or enforced input constraints', 'Output_Type': 'The method should raise an error, such as NullPointerException, due to @Nonnull constraint'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.*;
import org.junit.Test;
import software.amazon.event.ruler.SingleStateNameMatcher;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.NameState;

public class SingleStateNameMatcherTest {

    @Test
    public void testFindPatternWithNullReturnsExistingNameState() {
        // Given
        SingleStateNameMatcher matcher = new SingleStateNameMatcher();
        // Possibly expecting to get a predefined NameState before executing the test
        // Here, we directly extract what that might be without null path check
        try {
            // When
            NameState result = matcher.findPattern(null);

            // Then
            // Assuming `nameState` should always be a non-null, consistent return.
            // If it defaults to new NameState upon findPattern or through class setting safely.
            assertNotNull("The method should return actual NameState whether or not input was null, indicating default assignment or guard clause", result);
            
            // Add further basic assertions if there's behavior beyond instantiation worth confirming
            // e.g., default state sanity checks to distinguish real instances expected (if details available)
        } catch (Exception e) {
            fail("The method should not throw any exceptions: " + e);
        }
    }
}
