You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior of copying a non-empty byte array.', 'Input_Type': 'A non-empty byte array with arbitrary data.', 'Output_Type': 'A new byte array with the same length and content as the original.'}
Test Code:
package software.amazon.event.ruler.input;

import static org.junit.Assert.*;
import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.input.MultiByte;

public class MultiByteTest {

    private MultiByte nonEmptyMultiByte;

    @Before
    public void setUp() {
        // Initialize MultiByte with a non-empty byte array
        byte[] initialBytes = {10, 20, 30, 40, 50};
        nonEmptyMultiByte = new MultiByte(initialBytes);
    }

    @Test
    public void testGetBytesReturnsCopyOfNonEmptyByteArray() {
        // Execute: Call the getBytes method
        byte[] resultBytes = nonEmptyMultiByte.getBytes();

        // Verify: The length and content must be the same as the initial bytes
        assertArrayEquals("The returned byte array should have the same content as the original",
                new byte[]{10, 20, 30, 40, 50}, resultBytes);

        // Verify: Ensure it's a different reference, thus a true copy
        assertNotSame("The returned byte array should be a different instance from the original",
                nonEmptyMultiByte.getBytes(), resultBytes);
    }
}

Test Purpose: {'Test_Purpose': 'Validate behavior when the byte array is empty.', 'Input_Type': 'An empty byte array.', 'Output_Type': 'An empty byte array should be returned, indicating no loss or modification of data.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.input.MultiByte;

public class MultiByteTest {

    @Test(expected = IllegalArgumentException.class)
    public void testConstructorWithEmptyArrayThrowsException() {
        // Arrange & Act
        new MultiByte(); // Attempt to create a MultiByte object with an empty array

        // Assert is handled by the expected exception
    }
}

Test Purpose: {'Test_Purpose': "Test the method's response to the internal array being null (exceptional case).", 'Input_Type': 'A null byte array.', 'Output_Type': 'An exception should occur, likely a NullPointerException, indicating improper handling of null arrays.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Test;
import java.lang.reflect.InvocationTargetException;
import static org.junit.Assert.assertTrue;

public class MultiByteTest {

    @Test
    public void testGetBytes_NullByteArray_ShouldThrowNullPointerException() {
        try {
            MultiByte.class.getDeclaredConstructor(byte[].class).newInstance((Object) null);
        } catch (InvocationTargetException e) {
            // Check if cause is NullPointerException
            assertTrue(e.getCause() instanceof NullPointerException);
        } catch (Exception e) {
            // Any other exception type is not what we expect, fail the test
            throw new AssertionError("Unexpected exception type", e);
        }
    }
}
