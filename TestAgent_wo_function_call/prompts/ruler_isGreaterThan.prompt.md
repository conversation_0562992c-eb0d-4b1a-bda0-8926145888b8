You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Test normal behavior where the current MultiByte instance is greater than the other instance.', 'Input_Type': 'Both MultiByte instances have valid, positive values, with the current instance having a larger value than the other.', 'Output_Type': 'The output should be true, indicating the current instance is greater than the other instance.'}
Test Code:
package software.amazon.event.ruler.input;

import static org.junit.Assert.*;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.input.MultiByte;

public class MultiByteTest {
    
    private MultiByte multiByte1;
    private MultiByte multiByte2;

    @Before
    public void setUp() {
        // Set up mock objects
        multiByte1 = Mockito.mock(MultiByte.class);
        multiByte2 = Mockito.mock(MultiByte.class);

        // Define the behavior of isLessThanOrEqualTo method
        // multiByte2 is less than multiByte1, so isLessThanOrEqualTo should return false
        Mockito.when(multiByte1.isLessThanOrEqualTo(multiByte2)).thenReturn(false);
    }

    @Test
    public void testIsGreaterThan_whenCurrentInstanceIsGreater_thanTheOtherInstance() {
        // Invoke the method under test
        boolean result = multiByte1.isGreaterThan(multiByte2);

        // Verify the expected outcome
        assertTrue("The current instance should be greater than the other instance", result);
    }
}

Test Purpose: {'Test_Purpose': 'Test normal behavior where the current MultiByte instance is equal to the other instance.', 'Input_Type': 'Both MultiByte instances have valid, identical values.', 'Output_Type': 'The output should be false, indicating the current instance is not greater than the other instance.'}
Test Code:
package software.amazon.event.ruler.input;

import static org.junit.Assert.assertFalse;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.input.MultiByte;

public class MultiByteTest {

    @Test
    public void testIsGreaterThan_whenInstancesAreEqual() {
        // Mock the MultiByte instance for comparison
        MultiByte byteInstance1 = new MultiByte((byte) 1, (byte) 2, (byte) 3); // Example byte values
        MultiByte byteInstance2 = new MultiByte((byte) 1, (byte) 2, (byte) 3); // Same byte values as byteInstance1

        // Spy on the instances to mock isLessThanOrEqualTo
        MultiByte spyInstance1 = Mockito.spy(byteInstance1);
        MultiByte spyInstance2 = Mockito.spy(byteInstance2);

        // Mock the isLessThanOrEqualTo behavior
        Mockito.doReturn(true).when(spyInstance1).isLessThanOrEqualTo(spyInstance2);

        // Assert that isGreaterThan returns false when both instances are equal
        assertFalse(spyInstance1.isGreaterThan(spyInstance2));
    }

}

Test Purpose: {'Test_Purpose': 'Test boundary condition where the current MultiByte instance is slightly less than the other instance.', 'Input_Type': 'Both MultiByte instances have almost identical values with the other instance having a value greater by the smallest possible increment.', 'Output_Type': 'The output should be false, showing that the current instance is not greater than the other.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class MultiByteTest {

    private MultiByte current;
    private MultiByte slightlyGreaterOther;

    @Before
    public void setUp() {
        // Mocking the MultiByte instances
        current = mock(MultiByte.class);
        slightlyGreaterOther = mock(MultiByte.class);

        // Assume we have some way to represent the byte arrays indicating their values.

        // Mocking behavior for the isLessThanOrEqualTo method
        // Assume internal implementation correctly identifies that current is less than or equal to slightlyGreaterOther
        when(current.isLessThanOrEqualTo(slightlyGreaterOther)).thenReturn(true);
    }

    @Test
    public void testIsGreaterThan_slightlyLessThanOther() {
        // Invoke the method under test
        boolean result = current.isGreaterThan(slightlyGreaterOther);

        // Assertion
        assertFalse("Current MultiByte instance should not be greater than the slightly greater other instance.", result);
    }
}

Test Purpose: {'Test_Purpose': 'Test boundary condition where both MultiByte instances are at their maximum representable values.', 'Input_Type': 'Both MultiByte instances have their values set to the maximum possible, assuming some defined maximum.', 'Output_Type': 'The output should be false, since both instances are equal at their maximum values.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertFalse;
import software.amazon.event.ruler.input.MultiByte;

public class MultiByteTest {

    private MultiByte maxMultiByte1;
    private MultiByte maxMultiByte2;

    @Before
    public void setUp() {
        // Assuming specific constructor or factory method to get the maximum value possible for MultiByte
        // Using hypothetical method "ofMaxValue" to represent creation of a MultiByte instance with max values
        byte[] maxBytes = new byte[] {Byte.MAX_VALUE, Byte.MAX_VALUE}; // Example max value representation
        maxMultiByte1 = new MultiByte(maxBytes);
        maxMultiByte2 = new MultiByte(maxBytes);
    }

    @Test
    public void testIsGreaterThan_MaxBoundaryCondition() {
        assertFalse(maxMultiByte1.isGreaterThan(maxMultiByte2));
    }
}

Test Purpose: {'Test_Purpose': "Test exceptional case where the 'other' MultiByte instance is null.", 'Input_Type': 'The other instance is null, while the current instance has a valid value.', 'Output_Type': "The method should handle the null case gracefully, potentially by returning false or throwing an exception, depending on how 'isLessThanOrEqualTo' behaves with null."}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Test;
import software.amazon.event.ruler.input.MultiByte;
import static org.junit.Assert.*;

public class MultiByteTest {

    @Test
    public void testIsGreaterThanWithNullOther() {
        // Arrange
        MultiByte multiByte = new MultiByte((byte)0);

        try {
            // Act
            boolean result = multiByte.isGreaterThan(null);
            
            // Assert
            // Expected: Either false or handle the null gracefully by the method design
            assertFalse(result);  // Assuming the method returns false for null input
        } catch (NullPointerException e) {
            // If there is an expected exception, handle it here
            fail("Method does not handle null input gracefully");
        }
    }
}
