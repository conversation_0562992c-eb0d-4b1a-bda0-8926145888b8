You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify that the method returns the correct NameState object under normal conditions.', 'Input_Type': 'The NameState field is initialized with a valid NameState object.', 'Output_Type': 'The method should return the initialized NameState object.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.NameState;
import software.amazon.event.ruler.NameStateWithPattern;

public class NameStateWithPatternTest {

    private NameState mockNameState;
    private NameStateWithPattern nameStateWithPattern;

    @Before
    public void setUp() {
        // Mock the NameState object
        mockNameState = Mockito.mock(NameState.class);
        
        // Initialize NameStateWithPattern with the mocked NameState
        nameStateWithPattern = new NameStateWithPattern(mockNameState, null);
    }

    @Test
    public void testGetNameState() {
        // Retrieve the NameState using getNameState method
        NameState result = nameStateWithPattern.getNameState();
        
        // Assert that the returned NameState is the same as the one we initialized
        assertEquals("The returned NameState should be the same as the initialized one", mockNameState, result);
    }
}

Test Purpose: {'Test_Purpose': 'Assess how the method behaves when the NameState field is at its boundary condition, such as being null.', 'Input_Type': 'The NameState field is explicitly set to null.', 'Output_Type': 'The method should return null as the NameState field is null.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import org.mockito.Mockito;
import software.amazon.event.ruler.NameState;
import software.amazon.event.ruler.NameStateWithPattern;

public class NameStateWithPatternTest {

    private NameStateWithPattern nameStateWithPattern;

    @Before
    public void setUp() {
        // Here NameState is explicitly set to null as per test specification.
        nameStateWithPattern = new NameStateWithPattern(null, Mockito.mock(Patterns.class));
    }

    @Test
    public void testGetNameState_WhenNameStateIsNull_ShouldReturnNull() {
        // Act
        NameState result = nameStateWithPattern.getNameState();

        // Assert
        assertNull("The getNameState method should return null when the NameState field is null.", result);
    }
}

Test Purpose: {'Test_Purpose': "Evaluate method's behavior when the NameState field has not been explicitly set.", 'Input_Type': 'The NameState field is left uninitialized (default value).', 'Output_Type': 'The method should return the default-initialized value of the NameState field, which might be null or a default NameState object, depending on class initialization.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertNull;
import org.junit.Test;
import software.amazon.event.ruler.NameStateWithPattern;
import software.amazon.event.ruler.NameState;

public class NameStateWithPatternTest {

    @Test
    public void testGetNameState_DefaultInitialization() {
        // Create an instance of NameStateWithPattern where nameState is not explicitly set
        NameStateWithPattern nameStateWithPattern = new NameStateWithPattern(null, null);
        
        // Retrieve the nameState using the getter method
        NameState result = nameStateWithPattern.getNameState();
        
        // Assert that the nameState is null (default-initialized value)
        assertNull("Expected nameState to be null when not explicitly set", result);
    }
}
