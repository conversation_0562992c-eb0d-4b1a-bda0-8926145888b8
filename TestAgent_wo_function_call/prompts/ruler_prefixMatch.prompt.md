You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior of the method with a typical valid prefix.', 'Input_Type': "A typical valid string prefix, such as 'example'.", 'Output_Type': "An instance of ValuePatterns with MatchType.PREFIX and the given prefix 'example'."}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.ValuePatterns;
import software.amazon.event.ruler.MatchType;

public class PatternsTest {

    @Test
    public void testPrefixMatchWithValidPrefix() {
        // Given a typical valid prefix
        String prefix = "example";
        
        // When prefixMatch is called
        ValuePatterns result = Patterns.prefixMatch(prefix);
        
        // Then verify the ValuePatterns instance has MatchType.PREFIX and the given prefix
        assertEquals(MatchType.PREFIX, result.type());
        assertEquals(prefix, result.pattern());
    }
}

Test Purpose: {'Test_Purpose': 'Test the method with an empty prefix to verify behavior at boundary conditions.', 'Input_Type': 'An empty string as prefix.', 'Output_Type': 'An instance of ValuePatterns with MatchType.PREFIX and an empty string as the prefix.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.ValuePatterns;
import org.mockito.Mockito;

public class PatternsTest {

    @Test
    public void testPrefixMatchWithEmptyString() {
        // Arrange
        String emptyPrefix = "";
        
        // Act
        ValuePatterns result = Patterns.prefixMatch(emptyPrefix);
        
        // Assert
        assertNotNull("Result should not be null", result);
        assertEquals("Pattern should match an empty string", emptyPrefix, result.pattern());
        assertEquals("Match type should be PREFIX", MatchType.PREFIX, result.type());
    }
}

Test Purpose: {'Test_Purpose': "Test the method's behavior with a null prefix to cover exceptional cases.", 'Input_Type': 'A null value for the prefix.', 'Output_Type': 'The method either throws an exception (e.g., IllegalArgumentException) or handles null inputs gracefully, depending on implementation.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;

import org.junit.Test;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.ValuePatterns;

public class PatternsTest {

    @Test
    public void testPrefixMatchWithNull() {
        try {
            ValuePatterns result = Patterns.prefixMatch(null);
            assertNotNull("The result should not be null even if the input is null", result);
            // Depending on the design, you may want to check other attributes
            // of the result here if the handling of null is defined.
        } catch (IllegalArgumentException e) {
            // Expected behavior if null is not supported.
            // No need to fail the test in this case, just handle it gracefully.
        } catch (Exception e) {
            fail("An unexpected exception was thrown: " + e.getMessage());
        }
    }
}

Test Purpose: {'Test_Purpose': "Verify the method's behavior with a prefix containing special characters.", 'Input_Type': "A string prefix containing special characters, such as '@#$%'.", 'Output_Type': "An instance of ValuePatterns with MatchType.PREFIX and the given special character string '@#$%' as the prefix."}
Test Code:
package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.ValuePatterns;
import software.amazon.event.ruler.MatchType;

public class PatternsTest {

    @Test
    public void testPrefixMatchWithSpecialCharacters() {
        // Arrange
        String specialCharPrefix = "@#$%";

        // Act
        ValuePatterns result = Patterns.prefixMatch(specialCharPrefix);

        // Assert
        assertNotNull("Result should not be null", result);
        assertEquals("MatchType should be PREFIX", MatchType.PREFIX, result.type());
        assertEquals("The prefix should be '@#$%'", specialCharPrefix, result.pattern());
    }
}

Test Purpose: {'Test_Purpose': "Test the method's behavior with a very long string prefix to check for performance and handling of large inputs.", 'Input_Type': 'A very long string of characters (e.g., 10,000 characters).', 'Output_Type': 'An instance of ValuePatterns with MatchType.PREFIX and the long string as the prefix, ensuring no performance degradation or errors.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;

import org.junit.Test;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.ValuePatterns;
import software.amazon.event.ruler.MatchType;

public class PatternsTest {

    @Test
    public void testPrefixMatchWithLongString() {
        // Arrange
        String longPrefix = new String(new char[10000]).replace('\0', 'a'); // Generate a string with 10,000 'a's

        // Act
        ValuePatterns result = Patterns.prefixMatch(longPrefix);

        // Assert
        assertEquals(MatchType.PREFIX, result.type());
        assertEquals(longPrefix, result.pattern());
    }
}
