You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior by ensuring the method returns the expected pattern when a valid pattern is set.', 'Input_Type': "A valid Patterns object is set in the field 'pattern' of the instance.", 'Output_Type': 'The method should return the same Patterns object that was set in the instance.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import software.amazon.event.ruler.NameState;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.NameStateWithPattern;

public class NameStateWithPatternTest {

    private NameState mockNameState;
    private Patterns mockPattern;
    private NameStateWithPattern nameStateWithPattern;

    @Before
    public void setUp() {
        mockNameState = mock(NameState.class);
        mockPattern = mock(Patterns.class);
        nameStateWithPattern = new NameStateWithPattern(mockNameState, mockPattern);
    }

    @Test
    public void testGetPatternReturnsExpectedPattern() {
        // Assert that the pattern returned by getPattern is the one we set
        assertEquals(mockPattern, nameStateWithPattern.getPattern());
    }
}

Test Purpose: {'Test_Purpose': "Verify behavior when the 'pattern' field is null, simulating a boundary condition.", 'Input_Type': "The 'pattern' field of the instance is set to null.", 'Output_Type': "The method should return null, as the 'pattern' field is not initialized."}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.NameStateWithPattern;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.NameState;
import org.mockito.Mockito;

public class NameStateWithPatternTest {

    private NameStateWithPattern nameStateWithPattern;
    private NameState nameState;

    @Before
    public void setUp() {
        // Assuming NameState can be initialized with simple construction or obtain a mock.
        nameState = Mockito.mock(NameState.class);
        // Starting test, assume no specific behavior from mocked NameState.
        nameStateWithPattern = new NameStateWithPattern(nameState, null);
    }

    @Test
    public void testGetPattern_whenPatternIsNull_shouldReturnNull() {
        // Act
        Patterns result = nameStateWithPattern.getPattern();

        // Assert
        assertNull("The getPattern method should return null when the pattern field is null.", result);
    }
}

Test Purpose: {'Test_Purpose': 'Verify the immutability by checking if altering the returned Patterns object does not affect subsequent calls of getPattern.', 'Input_Type': 'A Patterns object is set and then modified externally after retrieval.', 'Output_Type': 'Subsequent calls to getPattern should still return the object unchanged, but if patterns are mutable, changes should reflect.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;

import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.NameStateWithPattern;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.NameState;  // Make sure you have this import, assuming NameState is accessible

public class NameStateWithPatternTest {

    private NameStateWithPattern nameStateWithPattern;
    private Patterns initialPattern;
    private NameState mockNameState;

    @Before
    public void setUp() {
        // Correctly set up the required NameState mock or instance
        mockNameState = mock(NameState.class); // Assume NameState is mockable or replace with appropriate setup if needed
        initialPattern = mock(Patterns.class);

        // Ensure no nulls are provided to the constructor
        nameStateWithPattern = new NameStateWithPattern(mockNameState, initialPattern);
    }

    @Test
    public void testGetPatternImmutability() {
        // Retrieve the pattern for the first time
        Patterns retrievedPattern = nameStateWithPattern.getPattern();
        
        // Assert that the initially set pattern is retrieved correctly
        assertEquals(initialPattern, retrievedPattern);

        // Attempt to modify the retrieved pattern
        // Note: Actual immutability would need verification against the Patterns class methods and expected behavior
        // For simplicity and illustrating test structure, we assume such methods are available if required

        Patterns subsequentRetrieval = nameStateWithPattern.getPattern();
        
        // Verify the pattern remains unchanged
        assertEquals("The pattern should remain unchanged.", initialPattern, subsequentRetrieval);
    }
}
