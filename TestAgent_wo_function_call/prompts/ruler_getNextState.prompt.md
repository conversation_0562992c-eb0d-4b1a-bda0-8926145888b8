You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior where nameState is set to a valid instance of NameState.', 'Input_Type': 'A valid instance of NameState assigned to the nameState field.', 'Output_Type': 'The method should return the exact instance of NameState that is assigned to nameState.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertSame;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.NameState;
import software.amazon.event.ruler.SingleStateNameMatcher;

import java.lang.reflect.Field;

public class SingleStateNameMatcherTest {

    private SingleStateNameMatcher singleStateNameMatcher;
    private NameState nameStateMock;

    @Before
    public void setUp() throws Exception {
        singleStateNameMatcher = new SingleStateNameMatcher();
        nameStateMock = Mockito.mock(NameState.class);

        // Use reflection to access and set the private field nameState
        Field nameStateField = SingleStateNameMatcher.class.getDeclaredField("nameState");
        nameStateField.setAccessible(true);
        nameStateField.set(singleStateNameMatcher, nameStateMock);
    }

    @Test
    public void testGetNextState_ReturnsAssignedNameState() {
        // Act
        NameState result = singleStateNameMatcher.getNextState();

        // Assert
        assertSame("getNextState should return the exact instance set in nameState", nameStateMock, result);
    }
}

Test Purpose: {'Test_Purpose': 'Test boundary condition where nameState might be assigned a boundary value within the valid NameState range.', 'Input_Type': 'An edge case instance of NameState, such as the smallest valid value or one with minimum configurations.', 'Output_Type': 'The method should return the edge case instance, verifying that boundary values are handled properly.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.*;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.NameState;
import software.amazon.event.ruler.SingleStateNameMatcher;

public class SingleStateNameMatcherTest {

    private SingleStateNameMatcher singleStateNameMatcher;
    private NameState mockNameState;

    @Before
    public void setUp() {
        // Create a mock NameState representing a boundary case
        mockNameState = Mockito.mock(NameState.class);
        
        // Initialize SingleStateNameMatcher and set the nameState
        singleStateNameMatcher = new SingleStateNameMatcher();
        setPrivateNameState(singleStateNameMatcher, mockNameState);
    }

    // A helper method to set the private field 'nameState' using reflection
    private void setPrivateNameState(SingleStateNameMatcher matcher, NameState state) {
        try {
            java.lang.reflect.Field field = SingleStateNameMatcher.class.getDeclaredField("nameState");
            field.setAccessible(true);
            field.set(matcher, state);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testGetNextState_BoundaryValue() {
        // Execute the method under test
        NameState result = singleStateNameMatcher.getNextState();
        
        // Verify the result is the same as the boundary instance
        assertSame("The method should return the edge case instance.", mockNameState, result);
    }
}

Test Purpose: {'Test_Purpose': 'Handle exceptional scenario where nameState is null.', 'Input_Type': 'nameState is explicitly set to null.', 'Output_Type': 'The method should return null, testing the system’s resilience to null values.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertNull;
import software.amazon.event.ruler.SingleStateNameMatcher;

public class SingleStateNameMatcherTest {

    private SingleStateNameMatcher singleStateNameMatcher;

    @Before
    public void setUp() {
        // Initialize the instance of SingleStateNameMatcher
        singleStateNameMatcher = new SingleStateNameMatcher();
        // Simulate the scenario where nameState is set to null
        // Since there's no setter, assuming direct field manipulation for the purpose of this test
        // This assumes the field is accessible or using reflection if it's private and not final
        setNameStateFieldToNull(singleStateNameMatcher);
    }

    private void setNameStateFieldToNull(SingleStateNameMatcher matcher) {
        try {
            // Using reflection to set nameState field to null
            java.lang.reflect.Field field = SingleStateNameMatcher.class.getDeclaredField("nameState");
            field.setAccessible(true);
            field.set(matcher, null);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set nameState field to null", e);
        }
    }

    @Test
    public void testGetNextStateWhenNameStateIsNull() {
        // Assert that getNextState returns null when nameState is null
        assertNull(singleStateNameMatcher.getNextState());
    }
}
