You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior of entries() with a non-empty collection.', 'Input_Type': 'A collection containing multiple Entry objects.', 'Output_Type': 'The expected output is an Iterable<Entry> that can be iterated over, returning each Entry object in the collection.'}
Test Code:
package software.amazon.event.ruler;

import software.amazon.event.ruler.IntIntMap;
import software.amazon.event.ruler.IntIntMap.Entry;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

public class IntIntMapTest {

    private IntIntMap intIntMap;

    @Before
    public void setUp() {
        intIntMap = new IntIntMap();
        // Add multiple Entry objects to the map
        intIntMap.put(1, 100);
        intIntMap.put(2, 200);
        intIntMap.put(3, 300);
    }

    @Test
    public void testEntriesWithNonEmptyCollection() {
        Iterable<Entry> entries = intIntMap.entries();
        assertNotNull("Entries should not be null", entries);

        Iterator<Entry> iterator = entries.iterator();
        assertNotNull("Iterator should not be null", iterator);

        // Store the entries in a set for comparison
        Set<String> keysSeen = new HashSet<>();
        Set<String> valuesSeen = new HashSet<>();

        while (iterator.hasNext()) {
            Entry entry = iterator.next();
            keysSeen.add(String.valueOf(entry.getKey()));
            valuesSeen.add(String.valueOf(entry.getValue()));
        }

        // Verify the entries were present in the iterable collection, without assuming order
        assertEquals("Unexpected number of keys", 3, keysSeen.size());
        assertTrue("Missing key 1", keysSeen.contains("1"));
        assertTrue("Missing key 2", keysSeen.contains("2"));
        assertTrue("Missing key 3", keysSeen.contains("3"));

        assertEquals("Unexpected number of values", 3, valuesSeen.size());
        assertTrue("Missing value 100", valuesSeen.contains("100"));
        assertTrue("Missing value 200", valuesSeen.contains("200"));
        assertTrue("Missing value 300", valuesSeen.contains("300"));
    }
}

Test Purpose: {'Test_Purpose': 'Check the behavior of entries() when the collection is empty.', 'Input_Type': 'An empty collection with no Entry objects.', 'Output_Type': 'The expected output is an Iterable<Entry> that allows iteration but does not yield any Entry objects.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.IntIntMap;
import java.util.Iterator;

public class IntIntMapTest {

    private IntIntMap intIntMap;

    @Before
    public void setUp() {
        intIntMap = new IntIntMap(); // Initialize the IntIntMap with default constructor
    }

    @Test
    public void testEntriesWhenEmpty() {
        Iterable<IntIntMap.Entry> entries = intIntMap.entries(); // Retrieve the iterable of entries
        Iterator<IntIntMap.Entry> iterator = entries.iterator(); // Obtain the iterator from the iterable
        assertFalse("The iterator should not have any entries to iterate over when the map is empty.", iterator.hasNext());
    }
}

Test Purpose: {'Test_Purpose': 'Test the boundary condition with a collection that contains a single Entry object.', 'Input_Type': 'A collection with exactly one Entry object.', 'Output_Type': 'The expected output is an Iterable<Entry> that returns the single Entry object when iterated over.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.IntIntMap;
import software.amazon.event.ruler.IntIntMap.Entry;

import java.util.Iterator;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertEquals;

public class IntIntMapTest {

    private IntIntMap intIntMap;

    @Before
    public void setUp() {
        intIntMap = new IntIntMap();
        intIntMap.put(1, 10);  // Adding a single entry for testing
    }

    @Test
    public void testEntriesWithSingleEntry() {
        Iterable<Entry> entries = intIntMap.entries();
        Iterator<Entry> iterator = entries.iterator();

        // Verify the iterator has a next entry
        assertTrue("Iterator should have next entry", iterator.hasNext());

        // Get the next entry and verify its key and value
        Entry entry = iterator.next();
        assertEquals("Entry key should be 1", 1, entry.getKey());
        assertEquals("Entry value should be 10", 10, entry.getValue());

        // Verify that there are no more entries
        assertTrue("Iterator should no longer have any entries", !iterator.hasNext());
    }
}

Test Purpose: {'Test_Purpose': 'Handle iteration after a modification attempt on the collection during iteration.', 'Input_Type': 'A collection is modified (if applicable) during iteration.', 'Output_Type': 'The expected output should either safely handle the modification by throwing an appropriate exception or by reflecting the modification in the iteration if such behavior is supported.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.IntIntMap;
import software.amazon.event.ruler.IntIntMap.Entry;

import java.util.Iterator;
import static org.junit.Assert.*;

public class IntIntMapTest {

    private IntIntMap intIntMap;

    @Before
    public void setUp() {
        intIntMap = new IntIntMap();
        intIntMap.put(1, 100);
        intIntMap.put(2, 200);
    }

    @Test(expected = java.util.ConcurrentModificationException.class) // Update to reflect typical collection behavior
    public void testModificationDuringIteration() {
        Iterable<Entry> entries = intIntMap.entries();
        Iterator<Entry> iterator = entries.iterator();

        // Iterate to the first element
        if (iterator.hasNext()) {
            Entry entry = iterator.next();

            // Modify the underlying collection
            intIntMap.put(3, 300);

            // Attempt to continue iteration should throw an exception
            if (iterator.hasNext()) {
                iterator.next(); // Actual code behavior should be confirmed.
            }
        }
    }
}
