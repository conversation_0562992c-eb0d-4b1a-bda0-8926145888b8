You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': "Verify that the method returns the correct value of 'key' under normal conditions.", 'Input_Type': "The 'key' field is initialized to a specific valid integer value.", 'Output_Type': "The method should return the same integer value as the 'key' field."}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;

import static org.junit.Assert.assertEquals;

public class EntryTest {

    private software.amazon.event.ruler.IntIntMap.Entry entry;

    @Before
    public void setUp() throws Exception {
        // Use reflection to create an instance of Entry and set the key and value fields
        Constructor<software.amazon.event.ruler.IntIntMap.Entry> constructor = 
          software.amazon.event.ruler.IntIntMap.Entry.class.getDeclaredConstructor(int.class, int.class);
        
        constructor.setAccessible(true);
        entry = constructor.newInstance(42, 100);
        
        // Optionally, set the key field directly if needed, provided the key field is non-final
        // Field keyField = software.amazon.event.ruler.IntIntMap.Entry.class.getDeclaredField("key");
        // keyField.setAccessible(true);
        // keyField.setInt(entry, 42);
    }

    @Test
    public void testGetKey() {
        // Test that the getKey method returns the correct key
        assertEquals(42, entry.getKey());
    }
}

Test Purpose: {'Test_Purpose': "Check the behavior of the method when 'key' is at the boundary of valid integer values.", 'Input_Type': "The 'key' field is initialized to Integer.MAX_VALUE.", 'Output_Type': 'The method should return Integer.MAX_VALUE.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.IntIntMap;
import software.amazon.event.ruler.IntIntMap.Entry;

public class EntryTest {

    private IntIntMap map;
    private Entry entry;

    @Before
    public void setUp() {
        // Initialize IntIntMap and add an entry
        map = new IntIntMap();
        map.put(Integer.MAX_VALUE, 0); // We assume 0 is a valid placeholder for the value.
        entry = map.entries().iterator().next();
    }

    @Test
    public void testGetKey_MaxValue() {
        int expected = Integer.MAX_VALUE;
        int actual = entry.getKey();
        assertEquals("The getKey() method should return Integer.MAX_VALUE when key is set to Integer.MAX_VALUE.", expected, actual);
    }
}

Test Purpose: {'Test_Purpose': "Check the behavior of the method when 'key' is at the lower boundary of valid integer values.", 'Input_Type': "The 'key' field is initialized to Integer.MIN_VALUE.", 'Output_Type': 'The method should return Integer.MIN_VALUE.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import java.util.Iterator;
import software.amazon.event.ruler.IntIntMap.Entry;
import software.amazon.event.ruler.IntIntMap;

public class EntryTest {

    private Entry entry;

    @Before
    public void setUp() {
        // Assuming there's a method in IntIntMap to add entries, this utilizes put method
        IntIntMap map = new IntIntMap();
        map.put(Integer.MIN_VALUE, 0); // Simulate entry creation
        Iterator<Entry> iterator = map.entries().iterator();
        if (iterator.hasNext()) {
            entry = iterator.next(); // Obtain an Entry instance through iteration
        }
    }

    @Test
    public void testGetKeyWithMinimumIntegerValue() {
        assertEquals("The getKey method should return Integer.MIN_VALUE when initialized with it.", Integer.MIN_VALUE, entry.getKey());
    }
}
