You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior for lowercase hexadecimal digits.', 'Input_Type': "Valid lowercase hexadecimal digits from '0' to 'f'.", 'Output_Type': 'Expected integer values based on Msb0 conversion.'}
Test Code:
package org.apache.commons.lang3;

import org.junit.Test;
import static org.junit.Assert.assertEquals;
import org.apache.commons.lang3.Conversion;

public class ConversionTest {

    @Test
    public void testHexDigitMsb0ToInt_ValidLowercaseDigits() {
        // Test for lowercase hexadecimal digits '0' to 'f'
        assertEquals(0, Conversion.hexDigitMsb0ToInt('0'));
        assertEquals(8, Conversion.hexDigitMsb0ToInt('1'));
        assertEquals(4, Conversion.hexDigitMsb0ToInt('2'));
        assertEquals(12, Conversion.hexDigitMsb0ToInt('3'));
        assertEquals(2, Conversion.hexDigitMsb0ToInt('4'));
        assertEquals(10, Conversion.hexDigitMsb0ToInt('5'));
        assertEquals(6, Conversion.hexDigitMsb0ToInt('6'));
        assertEquals(14, Conversion.hexDigitMsb0ToInt('7'));
        assertEquals(1, Conversion.hexDigitMsb0ToInt('8'));
        assertEquals(9, Conversion.hexDigitMsb0ToInt('9'));
        assertEquals(5, Conversion.hexDigitMsb0ToInt('a'));
        assertEquals(13, Conversion.hexDigitMsb0ToInt('b'));
        assertEquals(3, Conversion.hexDigitMsb0ToInt('c'));
        assertEquals(11, Conversion.hexDigitMsb0ToInt('d'));
        assertEquals(7, Conversion.hexDigitMsb0ToInt('e'));
        assertEquals(15, Conversion.hexDigitMsb0ToInt('f'));
    }
}

Test Purpose: {'Test_Purpose': 'Verify normal behavior for uppercase hexadecimal digits.', 'Input_Type': "Valid uppercase hexadecimal digits from '0' to 'F'.", 'Output_Type': 'Expected integer values based on Msb0 conversion.'}
Test Code:
package org.apache.commons.lang3;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import org.apache.commons.lang3.Conversion;
import org.junit.Test;

public class ConversionTest {

    @Test
    public void testHexDigitMsb0ToInt_UppercaseDigits() {
        // Test cases for valid uppercase hexadecimal digits
        try {
            assertEquals(0, Conversion.hexDigitMsb0ToInt('0'));
            assertEquals(1, Conversion.hexDigitMsb0ToInt('1'));
            assertEquals(2, Conversion.hexDigitMsb0ToInt('2'));
            assertEquals(3, Conversion.hexDigitMsb0ToInt('3'));
            assertEquals(4, Conversion.hexDigitMsb0ToInt('4'));
            assertEquals(5, Conversion.hexDigitMsb0ToInt('5'));
            assertEquals(6, Conversion.hexDigitMsb0ToInt('6'));
            assertEquals(7, Conversion.hexDigitMsb0ToInt('7'));
            assertEquals(8, Conversion.hexDigitMsb0ToInt('8'));
            assertEquals(9, Conversion.hexDigitMsb0ToInt('9'));
            assertEquals(10, Conversion.hexDigitMsb0ToInt('A'));
            assertEquals(11, Conversion.hexDigitMsb0ToInt('B'));
            assertEquals(12, Conversion.hexDigitMsb0ToInt('C'));
            assertEquals(13, Conversion.hexDigitMsb0ToInt('D'));
            assertEquals(14, Conversion.hexDigitMsb0ToInt('E'));
            assertEquals(15, Conversion.hexDigitMsb0ToInt('F'));
        } catch (IllegalArgumentException e) {
            fail("IllegalArgumentException should not have been thrown for valid characters.");
        }
    }
}

Test Purpose: {'Test_Purpose': 'Check behavior for non-hexadecimal characters.', 'Input_Type': "Invalid non-hexadecimal characters such as 'g', '@', or whitespace.", 'Output_Type': 'IllegalArgumentException is thrown.'}
Test Code:
package org.apache.commons.lang3;

import org.junit.Test;
import org.apache.commons.lang3.Conversion;

public class ConversionTest {

    @Test(expected = IllegalArgumentException.class)
    public void testHexDigitMsb0ToInt_InvalidCharacter() {
        // Test with an invalid non-hexadecimal character: 'g'
        Conversion.hexDigitMsb0ToInt('g');
    }

    @Test(expected = IllegalArgumentException.class)
    public void testHexDigitMsb0ToInt_InvalidSymbol() {
        // Test with an invalid non-hexadecimal character: '@'
        Conversion.hexDigitMsb0ToInt('@');
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testHexDigitMsb0ToInt_InvalidWhitespace() {
        // Test with an invalid whitespace character
        Conversion.hexDigitMsb0ToInt(' ');
    }
}

Test Purpose: {'Test_Purpose': 'Verify the boundary condition for edge-case hexadecimal digits.', 'Input_Type': "Hexadecimal digits near boundaries, such as '0', '1', 'e', 'f'.", 'Output_Type': 'Expected integer values as per predefined mapping.'}
Test Code:
package org.apache.commons.lang3;

import static org.junit.Assert.assertEquals;
import org.junit.Test;
import org.apache.commons.lang3.Conversion;

public class ConversionTest {

    @Test
    public void testHexDigitMsb0ToInt_boundaries() {
        // Test boundary hexadecimal digits and their expected integer values
        assertEquals("Expected conversion of '0' to produce 0.", 0, Conversion.hexDigitMsb0ToInt('0'));
        assertEquals("Expected conversion of '1' to produce 8.", 8, Conversion.hexDigitMsb0ToInt('1'));
        assertEquals("Expected conversion of 'e' to produce 7.", 7, Conversion.hexDigitMsb0ToInt('e'));
        assertEquals("Expected conversion of 'f' to produce 15.", 15, Conversion.hexDigitMsb0ToInt('f'));
    }
}
