You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Test normal behavior for a valid single-byte array', 'Input_Type': 'An array containing a single byte, e.g., new byte[]{5}', 'Output_Type': 'The single byte value within the array, e.g., 5'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Test;
import static org.junit.Assert.assertEquals;

public class MultiByteTest {

    @Test
    public void testSingularWithSingleByteArray() {
        // Arrange
        byte expectedByte = 5;
        MultiByte multiByte = new MultiByte(expectedByte);
        
        // Act
        byte actualByte = multiByte.singular();
        
        // Assert
        assertEquals("The singular method should return the byte value from a single-byte array.", expectedByte, actualByte);
    }
}

Test Purpose: {'Test_Purpose': 'Test exceptional case for an empty byte array', 'Input_Type': 'An empty byte array, e.g., new byte[]{}', 'Output_Type': "Throw an IllegalStateException with the message 'Must be a singular byte'"}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.input.MultiByte;

public class MultiByteTest {

    @Test(expected = IllegalArgumentException.class)
    public void testConstructorExceptionalCaseForEmptyArray() {
        // Given
        byte[] emptyArray = new byte[]{};

        // When
        new MultiByte(emptyArray);

        // Then
        // Expect IllegalArgumentException with the message "Must provide at least one byte"
    }
}

Test Purpose: {'Test_Purpose': 'Test exceptional case for a byte array with more than one element', 'Input_Type': 'An array containing multiple bytes, e.g., new byte[]{1, 2, 3}', 'Output_Type': "Throw an IllegalStateException with the message 'Must be a singular byte'"}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.input.MultiByte;

public class MultiByteTest {

    @Test(expected = IllegalStateException.class)
    public void testSingularWithMultipleBytes() {
        // Arrange: Create an instance of MultiByte with multiple bytes
        MultiByte multiByte = new MultiByte(new byte[]{1, 2, 3});

        // Act & Assert: Expect an IllegalStateException
        multiByte.singular();
    }
}
