You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Ensure the method returns the same instance of DefaultParser every time it is called, maintaining singleton property.', 'Input_Type': 'Invoke the getParser method multiple times.', 'Output_Type': 'The method should return the same instance of DefaultParser each time.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Assert;
import org.junit.Test;
import software.amazon.event.ruler.input.DefaultParser;

public class DefaultParserTest {

    @Test
    public void testGetParserReturnsSingletonInstance() {
        // Invoke getParser multiple times
        DefaultParser instance1 = DefaultParser.getParser();
        DefaultParser instance2 = DefaultParser.getParser();
        
        // Verify that each call returns the same instance
        Assert.assertSame("getParser should return the same instance on multiple invocations", instance1, instance2);
    }
}

Test Purpose: {'Test_Purpose': 'Verify that the method works correctly under concurrent access, ensuring the singleton instance is maintained.', 'Input_Type': 'Multiple threads concurrently call the getParser method.', 'Output_Type': 'All threads should receive the same instance of DefaultParser, guaranteeing thread safety.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Test;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import software.amazon.event.ruler.input.DefaultParser;

import static org.junit.Assert.assertSame;

public class DefaultParserTest {

    @Test
    public void testGetParserConcurrently() throws InterruptedException, ExecutionException {
        // Define a task to call getParser
        Callable<DefaultParser> task = DefaultParser::getParser;

        // Executes the callable in 10 threads
        ExecutorService executor = Executors.newFixedThreadPool(10);
        Future<DefaultParser>[] futures = new Future[10];
        for (int i = 0; i < 10; i++) {
            futures[i] = executor.submit(task);
        }

        // Get the parser instance from the first thread
        DefaultParser expectedInstance = futures[0].get();

        // Verify all threads receive the same instance of DefaultParser
        for (int i = 1; i < futures.length; i++) {
            assertSame(expectedInstance, futures[i].get());
        }

        executor.shutdown();
    }
}

Test Purpose: {'Test_Purpose': 'Check what happens when DefaultParser has not been properly instantiated or is null.', 'Input_Type': 'Simulate a condition where the SINGLETON is not initialized (if applicable).', 'Output_Type': "The method execution should handle this gracefully, but usually, it should not occur unless there's a fault in the setup."}
Test Code:
package software.amazon.event.ruler.input;

import static org.junit.Assert.*;
import org.junit.Test;
import software.amazon.event.ruler.input.DefaultParser;

public class DefaultParserTest {

    @Test
    public void testGetParserReturnsSingleton() {
        DefaultParser firstInstance = DefaultParser.getParser();
        DefaultParser secondInstance = DefaultParser.getParser();
        
        // Assert that the two instances are the same, confirming the singleton pattern
        assertSame("getParser should return the same instance", firstInstance, secondInstance);
    }
}
