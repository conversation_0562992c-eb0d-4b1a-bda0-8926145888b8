You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Validate normal behavior with a typical double value.', 'Input_Type': 'Valid double value successfully set using a setter method or constructor.', 'Output_Type': 'The method should return the exact double value that was initialized in the object.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;

import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.SubRuleContext;
import software.amazon.event.ruler.SubRuleContext.Generator;

public class SubRuleContextTest {

    private SubRuleContext subRuleContext;
    private final double testId = 42.42;

    @Before
    public void setUp() {
        Generator generator = new Generator();
        subRuleContext = generator.generate("TestRuleName");
    }

    @Test
    public void testGetId_ShouldReturnInitializedId() {
        assertEquals(testId, subRuleContext.getId(), 0.001);
    }
}

Test Purpose: {'Test_Purpose': 'Test behavior with boundary extreme positive double value.', 'Input_Type': 'The maximum positive double value that can be set (`Double.MAX_VALUE`).', 'Output_Type': 'The method should return the maximum positive double value.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;

public class SubRuleContextTest {

    private SubRuleContext subRuleContext;

    @Before
    public void setUp() {
        Object ruleName = new Object(); // Placeholder for a valid rule name
        SubRuleContext.Generator generator = new SubRuleContext.Generator();
        subRuleContext = generator.generate(ruleName);
        // manually set the id to Double.MAX_VALUE via the generator, assuming its implementation
    }

    @Test
    public void testGetIdWithMaxDoubleValue() {
        double expectedId = Double.MAX_VALUE;
        double actualId = subRuleContext.getId();
        assertEquals("The getId method should return the maximum positive double value.", expectedId, actualId, 0.0);
    }
}

Test Purpose: {'Test_Purpose': 'Test behavior with boundary extreme negative double value.', 'Input_Type': 'The minimum negative double value that can be set (`-Double.MAX_VALUE`).', 'Output_Type': 'The method should return the minimum negative double value.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.SubRuleContext;

public class SubRuleContextTest {

    private SubRuleContext subRuleContext;

    @Before
    public void setUp() {
        subRuleContext = Mockito.mock(SubRuleContext.class);
        final double minNegativeDouble = -Double.MAX_VALUE;
        Mockito.when(subRuleContext.getId()).thenReturn(minNegativeDouble);
    }

    @Test
    public void testGetIdWithMinimumNegativeDouble() {
        final double expectedValue = -Double.MAX_VALUE;
        double actualValue = subRuleContext.getId();
        assertEquals(expectedValue, actualValue, 0);
    }
}

Test Purpose: {'Test_Purpose': 'Check handling of the double value zero.', 'Input_Type': 'The double value zero set to the id field.', 'Output_Type': 'The method should return zero as a double value.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.SubRuleContext;
import software.amazon.event.ruler.SubRuleContext.Generator;

public class SubRuleContextTest {

    private Generator generator;

    @Before
    public void setUp() {
        generator = new SubRuleContext.Generator();
    }

    @Test
    public void testGetId_ForZeroValue() {
        // Arrange
        generator = new SubRuleContext.Generator();
        SubRuleContext subRuleContext = generator.generate(new Object());

        // Act
        double result = subRuleContext.getId();

        // Assert
        assertEquals(0.0, result, 0.0);
    }
}

Test Purpose: {'Test_Purpose': 'Test behavior when the id field is not initialized (exceptional case).', 'Input_Type': "A scenario where the default value of id (if any) is used, assuming it's not initialized specifically.", 'Output_Type': 'The method should return the default double value, which is commonly assumed to be 0.0 in absence of explicit initialization.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

import static org.junit.Assert.assertEquals;

public class SubRuleContextTest {

    private SubRuleContext subRuleContext;

    @Before
    public void setUp() throws NoSuchMethodException, IllegalAccessException, InvocationTargetException, InstantiationException {
        // Use reflection to access the private constructor
        Constructor<SubRuleContext> constructor = SubRuleContext.class.getDeclaredConstructor(double.class);
        constructor.setAccessible(true);
        subRuleContext = constructor.newInstance(0.0);
    }

    @Test
    public void testGetIdWhenIdNotInitialized() {
        double expectedId = 0.0;
        double actualId = subRuleContext.getId();
        assertEquals("The getId method should return the default double value of 0.0", expectedId, actualId, 0.0);
    }
}
