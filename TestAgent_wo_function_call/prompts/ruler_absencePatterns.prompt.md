You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior by checking that the method returns a Patterns instance with MatchType.ABSENT.', 'Input_Type': 'No input is required as this is a parameterless method.', 'Output_Type': 'A Patterns instance initialized with MatchType.ABSENT.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.assertEquals;
import org.junit.Test;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.MatchType;

public class PatternsTest {

    @Test
    public void testAbsencePatterns() {
        // Act
        Patterns result = Patterns.absencePatterns();
        
        // Assert
        assertEquals("The MatchType should be ABSENT", MatchType.ABSENT, result.type());
    }
}

Test Purpose: {'Test_Purpose': 'Check boundary conditions: Ensure that the method always returns a fresh Patterns instance and not a cached or singleton instance.', 'Input_Type': 'No input is required as this is a parameterless method.', 'Output_Type': 'Two separate invocations of the method should return distinct Patterns instances.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.Patterns;

public class PatternsTest {

    @Test
    public void testAbsencePatternsReturnsNewInstance() {
        // Invoke absencePatterns method twice
        Patterns patternInstance1 = Patterns.absencePatterns();
        Patterns patternInstance2 = Patterns.absencePatterns();
        
        // Assert that two separate invocations return distinct instances
        assertNotSame("Each call to absencePatterns should return a new instance", patternInstance1, patternInstance2);
    }
}

Test Purpose: {'Test_Purpose': 'Test exceptional case handling: Although the method does not handle inputs, ensure there are no side effects or exceptions when called repeatedly.', 'Input_Type': 'No input is provided as this is a parameterless method.', 'Output_Type': 'Consistent creation of Patterns instances without exceptions or unexpected behaviors.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.MatchType;

public class PatternsTest {

    private Patterns firstInstance;
    private Patterns secondInstance;

    @Before
    public void setUp() {
        firstInstance = Patterns.absencePatterns();
        secondInstance = Patterns.absencePatterns();
    }

    @Test
    public void testAbsencePatterns_NoExceptionOnRepeatedCalls() {
        // Assert that the instances are not null
        assertNotNull(firstInstance);
        assertNotNull(secondInstance);

        // Assert that the instances are different (i.e., new instance created each time)
        assertNotSame(firstInstance, secondInstance);

        // Checking that the MatchType of the created patterns is ABSENT
        assertEquals(MatchType.ABSENT, firstInstance.type());
        assertEquals(MatchType.ABSENT, secondInstance.type());
    }
}
