You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify function correctness with identical byte arrays', 'Input_Type': 'Valid input with identical byte arrays for both this.bytes and the parameter bytes', 'Output_Type': 'The function should return true indicating both arrays are identical'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.input.MultiByte;

import static org.junit.Assert.assertTrue;

public class MultiByteTest {

    private MultiByte multiByte;

    @Before
    public void setUp() {
        byte[] identicalBytes = {1, 2, 3, 4, 5};
        multiByte = new MultiByte(identicalBytes);
    }

    @Test
    public void testIsWithIdenticalByteArrays() {
        // Given
        byte[] identicalBytes = {1, 2, 3, 4, 5};
        
        // When
        boolean result = multiByte.is(identicalBytes);
        
        // Then
        assertTrue("The is method should return true for identical byte arrays", result);
    }
}

Test Purpose: {'Test_Purpose': 'Verify function behavior with different byte arrays', 'Input_Type': 'Valid input with different byte arrays for this.bytes and the parameter bytes', 'Output_Type': 'The function should return false indicating the arrays are not identical'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertFalse;

public class MultiByteTest {

    private MultiByte multiByte;

    @Before
    public void setUp() {
        // Initialize MultiByte with a specific byte array
        multiByte = new MultiByte(new byte[]{1, 2, 3});
    }

    @Test
    public void testIsDifferentByteArrays() {
        // Test with a different byte array than the one in the setup
        byte[] testBytes = new byte[]{4, 5, 6};
        // The expected result is false as the arrays are different
        boolean result = multiByte.is(testBytes);
        assertFalse("The method should return false when the arrays are different", result);
    }
}

Test Purpose: {'Test_Purpose': 'Check behavior with one byte array being a subset of the other', 'Input_Type': 'Valid input where one array is a prefix or suffix of the other', 'Output_Type': 'The function should return false because their lengths differ'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertFalse;
import software.amazon.event.ruler.input.MultiByte;

public class MultiByteTest {

    private MultiByte multiByte;

    @Before
    public void setUp() {
        // Assume that the MultiByte instance is initialized with the byte array {1, 2, 3}
        multiByte = new MultiByte((byte) 1, (byte) 2, (byte) 3);
    }

    @Test
    public void testIsWithPrefixByteArray() {
        byte[] prefixArray = {(byte) 1, (byte) 2}; // This is a prefix of the initialized array
        boolean result = multiByte.is(prefixArray);
        assertFalse("Expected false because the arrays have different lengths", result);
    }
}

Test Purpose: {'Test_Purpose': 'Validate behavior with empty byte arrays', 'Input_Type': 'Both this.bytes and the parameter bytes are empty arrays', 'Output_Type': 'The function should return true as both arrays are equal in terms of length and content'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertFalse;

public class MultiByteTest {

    private MultiByte multiByte;

    @Before
    public void setUp() {
        // Initialize MultiByte with a valid non-empty array for testing
        multiByte = new MultiByte(new byte[]{0}); // Use a valid single-byte array
    }

    @Test
    public void testIsWithEmptyByteArrays() {
        // Input: parameter bytes is an empty array
        byte[] testBytes = new byte[]{};

        // Execution & Assertion: The function should return false as the internal byte array is not empty
        assertFalse(multiByte.is(testBytes));

        // Additionally, verify behavior with matching non-empty array
        byte[] matchingBytes = new byte[]{0};
        assertTrue(multiByte.is(matchingBytes));
    }
}

Test Purpose: {'Test_Purpose': 'Test with mixed content in byte arrays', 'Input_Type': 'Valid input with this.bytes being [0, 1, 2] and the parameter bytes being [2, 1, 0]', 'Output_Type': 'The function should return false, as array contents differ in order'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Test;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.*;

public class MultiByteTest {

    @Test
    public void testIsWithMixedContentInByteArrays() {
        // Create a MultiByte instance with bytes [0, 1, 2]
        MultiByte multiByte = new MultiByte((byte) 0, (byte) 1, (byte) 2);
        
        // Use Mockito to mock the compare call if needed (here it's not essential as `Arrays.equals` is used directly)
        byte[] bytesToTest = new byte[]{2, 1, 0};
        
        // Assert that method 'is' returns false as the arrays differ in content order
        assertFalse(multiByte.is(bytesToTest));
    }
}
