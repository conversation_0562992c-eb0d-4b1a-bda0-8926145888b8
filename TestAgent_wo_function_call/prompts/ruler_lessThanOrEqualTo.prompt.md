You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Test normal condition with a typical string input.', 'Input_Type': 'A valid non-empty string.', 'Output_Type': 'A Range object with the byte array converted from the string as the upper bound (exclusive).'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.Range;

import java.nio.charset.StandardCharsets;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertFalse;

public class RangeTest {
    
    @Test
    public void testLessThanOrEqualToWithTypicalString() {
        // Arrange
        String input = "example";
        byte[] expectedTop = input.getBytes(StandardCharsets.UTF_8);
        
        // Act
        Range result = Range.lessThanOrEqualTo(input);
        
        // Mock expected behavior, assume top and openTop are directly accessible for illustration
        byte[] resultTop = result.top; // Mocking direct access to the `top` attribute
        boolean resultOpenTop = result.openTop; // Mocking direct access to the `openTop` status
        
        // Assert
        assertArrayEquals(expectedTop, resultTop);
        assertFalse(resultOpenTop);
    }
}

Test Purpose: {'Test_Purpose': 'Test boundary condition with an empty string.', 'Input_Type': 'An empty string input.', 'Output_Type': 'A Range object where the upper bound is the byte conversion of an empty string, exclusive.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.BeforeClass;
import org.junit.Test;
import static org.junit.Assert.*;
import org.mockito.Mockito;
import software.amazon.event.ruler.Range;

public class RangeTest {
    
    @BeforeClass
    public static void setUpClass() {
        // You can perform any global setup here if needed
    }

    @Test
    public void testLessThanOrEqualTo_WithEmptyString() {
        // Arrange
        String input = "";
        byte[] expectedUpperBound = stringToComparableBytes(input);

        // Act
        Range result = Range.lessThanOrEqualTo(input);

        // Assert
        assertArrayEquals("Upper bound should be exclusive of the byte conversion of the empty string.",
                expectedUpperBound, result.top);
        assertTrue("Upper bound should be exclusive.", result.openTop);
    }

    private static byte[] stringToComparableBytes(String string) {
        // Mock implementation for the conversion to simulate the private method
        // You may replace this with actual mock logic if the method can be called statically,
        // or if using Powermock to mock static methods were possible.
        return string.getBytes(); // Example conversion
    }
}

Test Purpose: {'Test_Purpose': 'Test exceptional case with a null input.', 'Input_Type': 'A null input, which is potentially invalid.', 'Output_Type': 'This should throw a NullPointerException or handle the null input gracefully, depending on the implementation of stringToComparableBytes and between.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.Range;

public class RangeTest {

    @Test(expected = NullPointerException.class)
    public void testLessThanOrEqualTo_NullInput_ThrowsException() {
        Range.lessThanOrEqualTo(null);
    }
}

Test Purpose: {'Test_Purpose': 'Test boundary case with the maximum length string.', 'Input_Type': "A string input with a very large length near the system's maximum array size.", 'Output_Type': 'A Range object with a corresponding large byte array as the upper bound, ensuring no performance or memory issues.'}
Test Code:
package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;

import software.amazon.event.ruler.Range;

public class RangeTest {

    @Test
    public void testLessThanOrEqualTo_LargeNumericString() {
        // Arrange
        String largeNumericString = "1".repeat(1000); // A large numeric string of valid syntax

        // Act
        Range result = Range.lessThanOrEqualTo(largeNumericString);

        // Assert
        assertNotNull("The result should not be null", result);

        // Additional assertions can go here to check specific properties of the result
    }
}

Test Purpose: {'Test_Purpose': 'Test normal condition with a numeric string.', 'Input_Type': "A string representation of numeric values (e.g., '123456').", 'Output_Type': 'A Range object with the byte array converted from the numeric string as the upper bound, exclusive.'}
Test Code:
package software.amazon.event.ruler;

import static org.junit.Assert.*;
import org.junit.Test;
import software.amazon.event.ruler.Range;

public class RangeTest {

    @Test
    public void testLessThanOrEqualTo_withNumericString() {
        // Arrange
        String numericString = "123456";
        
        // Act
        Range result = Range.lessThanOrEqualTo(numericString);
        
        // Assert
        assertNotNull(result);
        // asserting expected behavior using available methods or properties of result
        // Since we cannot access `stringToComparableBytes`, we focus on verifying logic consistency as much as possible
    }
}
