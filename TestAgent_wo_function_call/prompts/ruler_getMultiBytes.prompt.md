You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify the return value when multiBytes contains multiple elements.', 'Input_Type': 'multiBytes is a populated Set with multiple MultiByte objects.', 'Output_Type': 'The method should return a Set containing all the MultiByte instances present in multiBytes, preserving their order and count.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.input.InputMultiByteSet;
import software.amazon.event.ruler.input.MultiByte;

import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;

public class InputMultiByteSetTest {

    private InputMultiByteSet inputMultiByteSet;
    private Set<MultiByte> multiBytesMockSet;

    @Before
    public void setUp() {
        // Prepare a mock set of MultiByte objects
        multiBytesMockSet = new HashSet<>();
        MultiByte mb1 = Mockito.mock(MultiByte.class);
        MultiByte mb2 = Mockito.mock(MultiByte.class);
        MultiByte mb3 = Mockito.mock(MultiByte.class);

        multiBytesMockSet.add(mb1);
        multiBytesMockSet.add(mb2);
        multiBytesMockSet.add(mb3);

        // Initialize InputMultiByteSet with the mock set
        inputMultiByteSet = new InputMultiByteSet(multiBytesMockSet);
    }

    @Test
    public void testGetMultiBytes_ReturnsCorrectSet() {
        // Verify that the method returns the same set of MultiByte objects
        Set<MultiByte> result = inputMultiByteSet.getMultiBytes();
        assertEquals(multiBytesMockSet, result);
    }
}

Test Purpose: {'Test_Purpose': 'Verify behavior when multiBytes is empty.', 'Input_Type': 'multiBytes is an empty Set with no elements.', 'Output_Type': 'The method should return an empty Set, indicating there are no MultiByte instances stored.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import java.util.Collections;
import java.util.Set;
import software.amazon.event.ruler.input.InputMultiByteSet;
import software.amazon.event.ruler.input.MultiByte;

public class InputMultiByteSetTest {

    private InputMultiByteSet inputMultiByteSet;

    @Before
    public void setUp() {
        // Initialize with an empty Set of MultiByte
        inputMultiByteSet = new InputMultiByteSet(Collections.emptySet());
    }

    @Test
    public void testGetMultiBytesWhenEmpty() {
        // Act
        Set<MultiByte> result = inputMultiByteSet.getMultiBytes();

        // Assert
        assertNotNull("Result should not be null", result);
        assertTrue("Resulting Set should be empty", result.isEmpty());
    }
}

Test Purpose: {'Test_Purpose': 'Test the behavior with a single element in multiBytes.', 'Input_Type': 'multiBytes contains exactly one MultiByte object.', 'Output_Type': 'The method should return a Set containing that single MultiByte instance.'}
Test Code:
package software.amazon.event.ruler.input;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.input.InputMultiByteSet;
import software.amazon.event.ruler.input.MultiByte;

import java.util.Collections;
import java.util.Set;

import static org.junit.Assert.assertEquals;

public class InputMultiByteSetTest {

    private InputMultiByteSet inputMultiByteSet;
    private MultiByte mockMultiByte;

    @Before
    public void setUp() {
        mockMultiByte = Mockito.mock(MultiByte.class);
        inputMultiByteSet = new InputMultiByteSet(Collections.singleton(mockMultiByte));
    }

    @Test
    public void testGetMultiBytesWithSingleElement() {
        // Act
        Set<MultiByte> result = inputMultiByteSet.getMultiBytes();

        // Assert
        assertEquals(1, result.size());
        assertEquals(mockMultiByte, result.iterator().next());
    }
}

Test Purpose: {'Test_Purpose': 'Verify the behavior when multiBytes contains duplicate elements (if allowed).', 'Input_Type': 'multiBytes is a Set containing multiple references to identical MultiByte instances (if Set rules in the class allow duplicates).', 'Output_Type': 'The method should return a Set with unique MultiByte instances, and each unique instance should appear only once in the Set.'}
Test Code:
package software.amazon.event.ruler.input;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Before;
import org.junit.Test;
import java.util.HashSet;
import java.util.Set;
import software.amazon.event.ruler.input.InputMultiByteSet;
import software.amazon.event.ruler.input.MultiByte; // Assuming such a class exists

public class InputMultiByteSetTest {

    private InputMultiByteSet inputMultiByteSet;
    private Set<MultiByte> multiByteSet;

    @Before
    public void setUp() {
        // Create a mock MultiByte instance
        MultiByte mockMultiByte = mock(MultiByte.class);

        // Add the same Mock instance multiple times to the Set
        multiByteSet = new HashSet<>();
        multiByteSet.add(mockMultiByte);
        multiByteSet.add(mockMultiByte);

        // Instantiate the InputMultiByteSet with the mock Set
        inputMultiByteSet = new InputMultiByteSet(multiByteSet);
    }

    @Test
    public void testGetMultiBytesWhenDuplicatesPresent() {
        Set<MultiByte> result = inputMultiByteSet.getMultiBytes();

        // Assert the result set contains only one instance
        assertEquals("There should be only one unique instance in the set", 1, result.size());
        assertTrue("The result should contain the mocked MultiByte instance", result.containsAll(multiByteSet));
    }
}
