You are a senior Java engineer specialising in JUnit 5.
Below you will receive N pairs of:
	•	Test Purpose — a JSON object describing scenario, input type, and expected output.
	•	Test Code — an existing JUnit test that implements (or partially implements) that purpose.

Your task
	1.	Refactor & unify the supplied tests into one test class (OutlierTest, or a similarly appropriate name).
	2.	Keep each Test Purpose as an independent @Test method. Do not collapse multiple purposes into a single method.
	3.	Enhance readability & usability according to the rubric below.

Readability & Usability rubric  (💯 target full score)

Category	Goal	How to meet it
Naming Intuitiveness (3 pts)	Method & variable names are self-explanatory	• Camel-case method names that state condition + expected result  • Use @DisplayName for prose-style titles
Code Layout (3 pts)	Clear, DRY, logical structure	• Follow Arrange – Act – Assert • Extract common fixtures to @BeforeEach / helpers
Assertion Quality (3 pts)	Assertions exactly match the focal behaviour	• Use the most specific assertion (assertEquals, assertThrows, etc.) • Include custom failure messages
Adoption Effort (3 pts)	Test drops into a project with zero edits	• Pure JUnit 5 (org.junit.jupiter.*) • No randomness; deterministic constants • Only essential imports

Output format
	•	Return only the final Java test class, wrapped in a fenced code block ```java … ```.
	•	Do not include the production code or any prose outside the block.

Mandatory comments
	1.	Class-level Javadoc  ↳ overall what is covered.
	2.	Method-level Javadoc  ↳ copy the Test Purpose JSON as a @implNote or similar.
	3.	Inline comments (where non-obvious)  ↳ explain key inputs, edge cases, and why each assertion matters.
⸻

Test Purpose: {'Test_Purpose': 'Verify normal behavior with a typical set of number strings.', 'Input_Type': "A valid set of strings, each representing a number (e.g., {'1', '2', '3'}).", 'Output_Type': "An 'AnythingBut' instance initialized with the normalized set of numbers and the boolean flag set to true."}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.AnythingBut;
import software.amazon.event.ruler.ComparableNumber;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.*;

// Test class for Patterns
public class PatternsTest {

    @Test
    public void testAnythingButNumbersMatch() {
        // Test data: a set of number strings
        Set<String> numberStrings = new HashSet<>(Arrays.asList("1", "2", "3"));

        // Expected normalized numbers set using ComparableNumber.generate
        Set<String> expectedNormalizedNumbers = new HashSet<>();
        for (String number : numberStrings) {
            expectedNormalizedNumbers.add(ComparableNumber.generate(number));
        }

        // Execute the method under test
        AnythingBut result = Patterns.anythingButNumbersMatch(numberStrings);

        // Verify the results
        assertNotNull(result);
        assertEquals(expectedNormalizedNumbers, result.getValues());
        assertTrue(result.isNumeric());
    }
}

Test Purpose: {'Test_Purpose': 'Test behavior with an empty set to check boundary conditions.', 'Input_Type': 'An empty set of strings.', 'Output_Type': "An 'AnythingBut' instance with an empty set of normalized numbers and the boolean flag set to true."}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.AnythingBut;
import software.amazon.event.ruler.ComparableNumber;

import java.util.Set;
import java.util.HashSet;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class PatternsTest {

    @Test
    public void testAnythingButNumbersMatchWithEmptySet() {
        // Given an empty set of numbers
        Set<String> emptySet = new HashSet<>();

        // When the method under test is invoked
        AnythingBut result = Patterns.anythingButNumbersMatch(emptySet);

        // Then verify the result
        assertTrue(result.getValues().isEmpty());
        assertTrue(result.isNumeric());
    }
}

Test Purpose: {'Test_Purpose': 'Test behavior with invalid input containing non-numeric strings.', 'Input_Type': "A set of strings containing non-numeric values (e.g., {'a', 'b', 'c'}).", 'Output_Type': "Since 'ComparableNumber.generate' expects numeric inputs, the behavior depends on its implementation; typically an exception is thrown or handled internally."}
Test Code:
package software.amazon.event.ruler;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Set;
import java.util.HashSet;

import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.AnythingBut;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PatternsTest {

    @Test(expected = IllegalArgumentException.class)
    public void testAnythingButNumbersMatchWithNonNumericValues() {
        // Create a set containing non-numeric strings
        Set<String> nonNumericSet = new HashSet<>();
        nonNumericSet.add("a");
        nonNumericSet.add("b");
        nonNumericSet.add("c");

        // Call the method with the non-numeric set and expect it to throw IllegalArgumentException
        Patterns.anythingButNumbersMatch(nonNumericSet);
    }
}

Test Purpose: {'Test_Purpose': 'Check behavior with a single numeric string to ensure correct normalization.', 'Input_Type': "A set containing a single numeric string (e.g., {'42'}).", 'Output_Type': "An 'AnythingBut' instance initialized with a single normalized number and the boolean flag set to true."}
Test Code:
package software.amazon.event.ruler;

import org.junit.Test;
import static org.junit.Assert.*;
import software.amazon.event.ruler.Patterns;
import software.amazon.event.ruler.AnythingBut;

import java.util.Collections;
import java.util.Set;

public class PatternsTest {

    @Test
    public void testAnythingButNumbersMatch_singleNumber() {
        // Given
        Set<String> numbers = Collections.singleton("42");

        // When
        AnythingBut result = Patterns.anythingButNumbersMatch(numbers);

        // Then
        assertNotNull("Resulting AnythingBut object should not be null", result);
        assertEquals("Resulting set should contain one normalized number", 1, result.getValues().size());
        assertTrue("Resulting set should contain the normalized number '42'", result.getValues().contains("42")); // Assuming normalization keeps the number as is
        assertTrue("The boolean flag should be set to true", result.isNumeric());
    }
}

Test Purpose: {'Test_Purpose': 'Verify behavior with duplicate numbers in input.', 'Input_Type': "A set of number strings with duplicates (e.g., {'1', '1', '2'}).", 'Output_Type': "An 'AnythingBut' instance with duplicates removed in processing, containing only unique normalized numbers and the boolean flag set to true."}
Test Code:
package software.amazon.event.ruler;

import org.junit.Before;
import org.junit.Test;
import software.amazon.event.ruler.ComparableNumber;
import software.amazon.event.ruler.AnythingBut;
import software.amazon.event.ruler.Patterns;

import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;

public class PatternsTest {

    @Test
    public void testAnythingButNumbersMatchWithDuplicates() {
        // Given
        Set<String> input = new HashSet<>();
        input.add("1");
        input.add("1");
        input.add("2");

        // When
        AnythingBut result = Patterns.anythingButNumbersMatch(input);

        // Then
        Set<String> expectedNormalizedSet = new HashSet<>();
        expectedNormalizedSet.add(ComparableNumber.generate("1"));
        expectedNormalizedSet.add(ComparableNumber.generate("2"));

        assertEquals(expectedNormalizedSet, result.getValues());
        assertEquals(true, result.isNumeric());
    }
}
