/*
 * This file was automatically generated by EvoSuite
 * Wed Jun 18 06:27:40 GMT 2025
 */


import org.junit.Test;
import static org.junit.Assert.*;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true) 
public class TestClass_ESTest extends TestClass_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      String string0 = TestClass.javascriptEscape((String) null);
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      String string0 = TestClass.javascriptEscape("");
      assertEquals("", string0);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      boolean boolean0 = TestClass.isTopOrBottom("bottom");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      boolean boolean0 = TestClass.isTopOrBottom("");
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      boolean boolean0 = TestClass.isTopOrBottom("top");
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      String string0 = testClass0.transform("", "");
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      testClass0.setPaint("WindPlot: type");
      assertEquals("type", testClass0.getType());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      boolean boolean0 = testClass0.isLeft();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      boolean boolean0 = testClass0.isBaseline();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      double double0 = testClass0.getLeft();
      assertEquals(50.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      String string0 = testClass0.getType();
      assertEquals("type", string0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      boolean boolean0 = testClass0.isMultiple();
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      Object object0 = new Object();
      int int0 = testClass0.compareTo(object0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      double double0 = testClass0.getRadius();
      assertEquals(25.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      String string0 = testClass0.createWindPlot("", "", "", "", true, true, true);
      assertEquals("WindPlot: ", string0);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      double double0 = testClass0.getY();
      assertEquals(75.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      double double0 = testClass0.trimWidth(0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      boolean boolean0 = testClass0.overlaps(testClass0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      String string0 = testClass0.getPaint();
      assertEquals("paint", string0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      boolean boolean0 = testClass0.isVerticalCenter();
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      double double0 = testClass0.getHeight();
      assertEquals(100.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      TestClass testClass0 = new TestClass();
      testClass0.setPoint("UK`FF");
      assertEquals("paint", testClass0.getPaint());
  }
}
