from langchain_core.prompts import ChatPromptTemplate
import sys
sys.path.append("/Users/<USER>/Desktop/TestAgents")

import os
import re
import json
from ExecuteTools import compile_and_execute_test, simple_fix
from langchain_openai import ChatOpenAI
from CKGRetriever import CKGRetriever
from EnvironmentService import EnvironmentService
from Node import Clazz, Method, Variable
from RetrieverTools import (find_class, find_method_definition, find_variable_definition, find_method_calls,
                            find_method_usages, fuzzy_search)
from Utils import add_record, add_test_case
from Config import llm
from datetime import datetime
from LoggerManager import LoggerManager
import tiktoken
log_dir = "result/chart-0503-gpt_4o"
logger_manager = LoggerManager()
logger = logger_manager.logger

encoding = tiktoken.encoding_for_model("gpt-4o")
# graph_retriever = CKGRetriever("bolt://localhost:7687", "neo4j", "123456")
envServer = EnvironmentService()
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-csv-rel-commons-csv-1.10.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/gson-gson-parent-2.12.0/gson")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/windward-1.5.3-RELEASE")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/event-ruler-1.8.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-lang-rel-commons-lang-3.10")
envServer.set_base_path("/Users/<USER>/Desktop/java-project/jfreechart-1.5.5")
# methods_list = graph_retriever.load_methods(5)
# if commons-lang and jfreechart, use this
# methods_list = graph_retriever.load_filtered_methods()
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

project_name = "chart"
OUTPUT_DIR = "/Users/<USER>/Desktop/TestAgents/HITs/readable"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

filter_method = [
        "javascriptEscape",
        "windowActivated",
        "getY",
        "getLeft",
        "getHeight",
        "getType",
        "setPaint",
        "isLeft",
        "createWindPlot",
        "trimWidth",
        "getPaint",
        "setPoint",
        "getRadius",
        "transform",
        "isTopOrBottom",
        "compareTo",
        "isVerticalCenter",
        "isMultiple",
        "overlaps",
        "isBaseline",
    ]

def read_jsonl(file_path):
    ret = []
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            data = json.loads(line)
            method_read = Method(
                id=data["id"],
                name=data["name"],
                class_name=data["class_name"],
                package_name=data["package_name"],
                signature=data["signature"],
                content=data["content"],
                start_line=data["start_line"],
                end_line=data["end_line"],
                absolute_path=data["absolute_path"],
                full_qualified_name=data["full_qualified_name"],
                params=data["params"],
                return_type=data["return_type"],
                modifiers=data["modifiers"],
                exception_throw=data["exception_throw"],
                type=data["type"],
                summarization=data["summarization"],
                comment=data["comment"]
            )
            ret.append(method_read)
        return ret

def write_jsonl(data, path):
    with open(path, 'a', encoding='utf-8') as f:
        for item in data:
            json_line = json.dumps(item, ensure_ascii=False)
            f.write(json_line + '\n')

def extract_class_name(full_qualified_name: str, package_name: str) -> str:
    # 去掉包名部分
    remainder = full_qualified_name[len(package_name) + 1:]  # +1 是为了去掉点号
    # 使用 '$' 或 '.' 分割，获取类名
    if '$' in remainder:
        class_name = remainder.split('$')[0]
    else:
        class_name = remainder.split('.')[0]
    return class_name

methods_list = read_jsonl("/Users/<USER>/Desktop/TestAgents/TestAgent_wo_function_call/result/focal_method/chart.jsonl")

def calculate_token(text):
    num_tokens = len(encoding.encode(text))
    return num_tokens


def extract_java_test_case(input_string):
    # 使用正则表达式查找Java代码块
    pattern = r"```java(.*?)```"
    matches = re.findall(pattern, input_string, re.DOTALL)
    for match in matches:
        # 检查是否包含测试用例相关字段
        if "@Test" in match:
            return envServer.simple_fix(match.strip())
    return None


if __name__ == "__main__":

    index = 0
    # assert len(methods_list) == 200
    result_path = log_dir + "/chart_result_new"
    if not os.path.exists(result_path):
        os.makedirs(result_path)
    for method in methods_list:
        index += 1
        # if index < 135:
        #     continue
        if method.name not in filter_method:
            continue
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] index: {index}")
        envServer.remove_test_case()
        if not os.path.exists(f"{log_dir}/detail/{str(index) + '_' + method.name}"):
            os.makedirs(f"{log_dir}/detail/{str(index) + '_' + method.name}")
        log_path = f"{log_dir}/detail/{str(index) + '_' + method.name}/{index}_{method.name}.log"
        logger_manager.change_log_path(log_path)
        full_qualified_name = method.full_qualified_name
        inject_path = method.absolute_path.replace("src/main/java", "src/test/java")
        inject_dir = inject_path[:inject_path.rfind("/")]
        name = method.name
        package_name = method.package_name
        start_line = method.start_line
        end_line = method.end_line
        class_name = extract_class_name(full_qualified_name, package_name)
        full_class_name = method.package_name + "." + class_name
        generate_ret = envServer.generate_test_case(full_class_name, name)
        # generate_ret = True, "OK"
        generate_output = generate_ret[1]
        logger.info(f"generate_output: {generate_output}")
        if generate_ret[0]:
            move_ret = envServer.move_test_case(inject_dir)
            # assert len(move_ret) == 1
            logger.info(f"move_ret: {move_ret[1]}")
            if move_ret[0]:
                for file_path in move_ret[1]:
                    with open(file_path, "r") as f:
                        content = f.read()
                        if content is not None:
                            with open(OUTPUT_DIR + "/" + project_name + "_" + name + ".java", "a") as f:
                                f.write(content + "\n\n\n")
        #         run_ret = envServer.direct_run_compile_execute_test()
        #         logger.info(f"run_ret: {run_ret['output']}")
        #         if run_ret['result'] == "Compile Error":
        #             logger.info("Compile Error")
        #             save_data = [{"name": str(index) + '_' + name, "result": "Compile Error", "line_coverage": 0, "branch_coverage": 0, "mutation_score": 0}]
        #             write_jsonl(save_data, result_path + "/ret.jsonl")
        #             continue
        #         elif run_ret['result'] == "Execute Error":
        #             coverage_ret = envServer.simple_run_coverage_test(package_name, class_name, "", start_line, end_line)
        #             logger.info(f"compile_ret: {coverage_ret}")
        #             if coverage_ret['result'] == "Success":
        #                 line_coverage = coverage_ret['output']['line_coverage']
        #                 branch_coverage = coverage_ret['output']['branch_coverage']
        #             else:
        #                 line_coverage = 0
        #                 branch_coverage = 0
        #             logger.info("Execute Error")
        #             save_data = [{"name": str(index) + '_' + name,  "result": "Execute Error", "line_coverage": line_coverage, "branch_coverage": branch_coverage, "mutation_score": 0}]
        #             write_jsonl(save_data, result_path + "/ret.jsonl")
        #             continue
        #         elif run_ret['result'] == "Success":
        #             coverage_ret = envServer.simple_run_coverage_test(package_name, class_name, "",
        #                                                               start_line, end_line)
        #             logger.info(f"compile_ret: {coverage_ret}")
        #             if coverage_ret['result'] == "Success":
        #                 line_coverage = coverage_ret['output']['line_coverage']
        #                 branch_coverage = coverage_ret['output']['branch_coverage']
        #             else:
        #                 line_coverage = 0
        #                 branch_coverage = 0
        #             mutation_ret = envServer.simple_run_mutation_test(name, class_name, start_line, end_line)
        #             logger.info(f"mutation_ret: {mutation_ret}")
        #             if mutation_ret['result'] == "Success":
        #                 mutation_score = mutation_ret['output']['mutation_score']
        #             else:
        #                 mutation_score = 0
        #             logger.info("Success")
        #             save_data = [{"name": str(index) + '_' + name, "result": "Success", "line_coverage": line_coverage, "branch_coverage": branch_coverage,
        #                           "mutation_score": mutation_score}]
        #             write_jsonl(save_data, result_path + "/ret.jsonl")
        #             continue
        #     else:
        #         logger.info("move test case failed")
        #         save_data = [{"name": str(index) + '_' + name, "result": "Compile Error", "line_coverage": 0, "branch_coverage": 0, "mutation_score": 0}]
        #         write_jsonl(save_data, result_path + "/ret.jsonl")
        #         continue
        # else:
        #     logger.info("generate test case failed")
        #     save_data = [{"name": str(index) + '_' + name, "result": "Compile Error", "line_coverage": 0, "branch_coverage": 0, "mutation_score": 0}]
        #     write_jsonl(save_data, result_path + "/ret.jsonl")
        #     continue