package org.jfree.chart.ui;

import org.junit.Test;
import static org.junit.Assert.*;
import org.mockito.*;
import org.junit.jupiter.api.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.io.ObjectStreamException;
import java.io.Serializable;

public class slice0_TextAnchor_isBaseline_5_0_Test {

    @Test
    public void testIsBaselineForBaselineLeft() {
        // Arrange
        TextAnchor anchor = TextAnchor.BASELINE_LEFT;
        // Act
        boolean result = anchor.isBaseline();
        // Assert
        assertTrue("TextAnchor.BASELINE_LEFT should be identified as baseline", result);
    }

    @Test
    public void testIsBaselineForBaselineCenter() {
        // Arrange
        TextAnchor anchor = TextAnchor.BASELINE_CENTER;
        // Act
        boolean result = anchor.isBaseline();
        // Assert
        assertTrue("TextAnchor.BASELINE_CENTER should be identified as baseline", result);
    }

    @Test
    public void testIsBaselineForBaselineRight() {
        // Arrange
        TextAnchor anchor = TextAnchor.BASELINE_RIGHT;
        // Act
        boolean result = anchor.isBaseline();
        // Assert
        assertTrue("TextAnchor.BASELINE_RIGHT should be identified as baseline", result);
    }

    @Test
    public void testIsBaselineForTopLeft() {
        // Arrange
        TextAnchor anchor = TextAnchor.TOP_LEFT;
        // Act
        boolean result = anchor.isBaseline();
        // Assert
        assertFalse("TextAnchor.TOP_LEFT should not be identified as baseline", result);
    }

    @Test
    public void testIsBaselineForCenter() {
        // Arrange
        TextAnchor anchor = TextAnchor.CENTER;
        // Act
        boolean result = anchor.isBaseline();
        // Assert
        assertFalse("TextAnchor.CENTER should not be identified as baseline", result);
    }
}



package org.jfree.chart.ui;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import org.junit.Test;
import org.mockito.*;
import org.junit.jupiter.api.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.io.ObjectStreamException;
import java.io.Serializable;

public class slice3_TextAnchor_isBaseline_5_0_Test {

    @Test
    public void testIsBaselineWithBaselineLeft() {
        TextAnchor anchor = TextAnchor.BASELINE_LEFT;
        assertTrue("Expected BASELINE_LEFT to be baseline", anchor.isBaseline());
    }

    @Test
    public void testIsBaselineWithBaselineCenter() {
        TextAnchor anchor = TextAnchor.BASELINE_CENTER;
        assertTrue("Expected BASELINE_CENTER to be baseline", anchor.isBaseline());
    }

    @Test
    public void testIsBaselineWithBaselineRight() {
        TextAnchor anchor = TextAnchor.BASELINE_RIGHT;
        assertTrue("Expected BASELINE_RIGHT to be baseline", anchor.isBaseline());
    }

    @Test
    public void testIsBaselineWithTopLeft() {
        TextAnchor anchor = TextAnchor.TOP_LEFT;
        assertFalse("Expected TOP_LEFT not to be baseline", anchor.isBaseline());
    }

    @Test
    public void testIsBaselineWithCenter() {
        TextAnchor anchor = TextAnchor.CENTER;
        assertFalse("Expected CENTER not to be baseline", anchor.isBaseline());
    }

    @Test
    public void testIsBaselineWithBottomRight() {
        TextAnchor anchor = TextAnchor.BOTTOM_RIGHT;
        assertFalse("Expected BOTTOM_RIGHT not to be baseline", anchor.isBaseline());
    }
}



package org.jfree.chart.ui;

import org.junit.Test;
import static org.junit.Assert.*;
import org.mockito.*;
import org.junit.jupiter.api.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.io.ObjectStreamException;
import java.io.Serializable;

public class slice1_TextAnchor_isBaseline_5_0_Test {

    @Test
    public void testIsBaselineForBaselineCenter() {
        // Arrange
        TextAnchor textAnchor = TextAnchor.BASELINE_CENTER;
        // Act
        boolean result = textAnchor.isBaseline();
        // Assert
        assertTrue("TextAnchor.BASELINE_CENTER should be considered as baseline.", result);
    }

    @Test
    public void testIsBaselineForBaselineLeft() {
        // Arrange
        TextAnchor textAnchor = TextAnchor.BASELINE_LEFT;
        // Act
        boolean result = textAnchor.isBaseline();
        // Assert
        assertTrue("TextAnchor.BASELINE_LEFT should be considered as baseline.", result);
    }

    @Test
    public void testIsBaselineForBaselineRight() {
        // Arrange
        TextAnchor textAnchor = TextAnchor.BASELINE_RIGHT;
        // Act
        boolean result = textAnchor.isBaseline();
        // Assert
        assertTrue("TextAnchor.BASELINE_RIGHT should be considered as baseline.", result);
    }

    @Test
    public void testIsBaselineForNonBaseline() {
        // Arrange
        TextAnchor textAnchor = TextAnchor.TOP_LEFT;
        // Act
        boolean result = textAnchor.isBaseline();
        // Assert
        assertFalse("TextAnchor.TOP_LEFT should not be considered as baseline.", result);
    }
}



package org.jfree.chart.ui;

import org.junit.Test;
import static org.junit.Assert.*;
import org.mockito.*;
import org.junit.jupiter.api.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.io.ObjectStreamException;
import java.io.Serializable;

public class slice2_TextAnchor_isBaseline_5_0_Test {

    @Test
    public void testIsBaselineForBaselineRight() {
        // Arrange
        TextAnchor baselineRight = TextAnchor.BASELINE_RIGHT;
        // Act
        boolean result = baselineRight.isBaseline();
        // Assert
        assertTrue("Expected BASELINE_RIGHT to be identified as a baseline anchor", result);
    }

    @Test
    public void testIsBaselineForNonBaselineAnchors() {
        // Arrange
        TextAnchor[] nonBaselineAnchors = { TextAnchor.TOP_LEFT, TextAnchor.TOP_CENTER, TextAnchor.TOP_RIGHT, TextAnchor.HALF_ASCENT_LEFT, TextAnchor.HALF_ASCENT_CENTER, TextAnchor.HALF_ASCENT_RIGHT, TextAnchor.CENTER_LEFT, TextAnchor.CENTER, TextAnchor.CENTER_RIGHT, TextAnchor.BOTTOM_LEFT, TextAnchor.BOTTOM_CENTER, TextAnchor.BOTTOM_RIGHT };
        // Act & Assert
        for (TextAnchor anchor : nonBaselineAnchors) {
            assertFalse("Expected " + anchor + " not to be identified as a baseline anchor", anchor.isBaseline());
        }
    }
}



