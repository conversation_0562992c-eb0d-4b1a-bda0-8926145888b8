from langchain_openai import ChatOpenAI
from langchain_ollama.llms import OllamaLLM
import os

# LLM信息
# os.environ["OPENAI_API_BASE"] = "https://api.deepseek.com/v1"
# os.environ["OPENAI_API_KEY"] = "sk-58dd68ac267d494fac181f3007317a8f"
# llm = ChatOpenAI(model="deepseek-chat")

# llm = ChatOpenAI(
#     api_key="deepseek",
#     model="deepseek-r1:70b",
#     base_url="http://localhost:11434/v1",
# )

os.environ["OPENAI_API_BASE"] = "https://api.geekai.pro/v1"
os.environ["OPENAI_API_KEY"] = "sk-NaqQSut7cM0pqYVSFaF95dF7D854422b9f7478Db887749B9"
llm = ChatOpenAI(model="gpt-4o-2024-08-06")


